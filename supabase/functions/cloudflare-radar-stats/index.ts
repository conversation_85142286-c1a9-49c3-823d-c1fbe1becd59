
import { serve } from "https://deno.land/std@0.190.0/http/server.ts";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

const RADAR_BASE_URL = "https://api.cloudflare.com/client/v4/radar";
const REQUEST_TIMEOUT_MS = 30_000;
const MAX_RETRIES = 3;

/**
 * Handle CORS preflight requests
 */
function handleOptionsRequest() {
  return new Response("ok", {
    headers: {
      ...corsHeaders,
      "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
    },
  });
}

/**
 * Retry logic for transient errors
 */
function isRetryableError(error) {
  return (
    error.name === "AbortError" ||
    error.message.includes("timeout") ||
    error.message.includes("ECONNRESET") ||
    error.message.includes("503")
  );
}

/**
 * Perform a fetch with timeout and retries
 */
async function fetchWithRetry(url, apiToken, retries = MAX_RETRIES) {
  const controller = new AbortController();
  const timeout = setTimeout(() => controller.abort(), REQUEST_TIMEOUT_MS);

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${apiToken}`,
        "Content-Type": "application/json",
      },
      signal: controller.signal,
    });

    clearTimeout(timeout);

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    if (!data.success) {
      throw new Error(`Cloudflare API returned error: ${JSON.stringify(data.errors)}`);
    }

    return data.result;
  } catch (err) {
    clearTimeout(timeout);

    if (retries > 0 && isRetryableError(err)) {
      console.warn(`Retrying fetch: ${url} (${MAX_RETRIES - retries + 1}/${MAX_RETRIES})`);
      await new Promise((res) => setTimeout(res, 1000));
      return fetchWithRetry(url, apiToken, retries - 1);
    }

    console.error(`Fetch failed: ${url}`, err);
    return null; // Allow partial results if one call fails
  }
}

/**
 * Fetch multiple Radar datasets concurrently
 */
async function fetchRadarData(apiToken) {
  const now = new Date();
  const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
  const dateRange = `${oneWeekAgo.toISOString().split("T")[0]},${now.toISOString().split("T")[0]}`;

  const endpoints = {
    layer7ByIndustry: `${RADAR_BASE_URL}/attacks/layer7/summary/industry?dateRange=${dateRange}&format=json`,
    emailMalicious: `${RADAR_BASE_URL}/email/security/summary/malicious?dateRange=${dateRange}&format=json`,
    emailDmarc: `${RADAR_BASE_URL}/email/routing/summary/dmarc?dateRange=${dateRange}&format=json`,
    emailDkim: `${RADAR_BASE_URL}/email/security/summary/dkim?dateRange=${dateRange}&format=json`,
    emailSpf: `${RADAR_BASE_URL}/email/security/summary/spf?dateRange=${dateRange}&format=json`,
    emailSpam: `${RADAR_BASE_URL}/email/security/summary/spam?dateRange=${dateRange}&format=json`,
    emailSpoof: `${RADAR_BASE_URL}/email/security/summary/spoof?dateRange=${dateRange}&format=json`,
  };

  const results = await Promise.all(
    Object.entries(endpoints).map(async ([key, url]) => {
      const data = await fetchWithRetry(url, apiToken);
      return { [key]: data };
    })
  );

  return Object.assign({}, ...results);
}

/**
 * Main handler
 */
serve(async (req) => {
  if (req.method === "OPTIONS") {
    return handleOptionsRequest();
  }

  const apiToken = Deno.env.get("CLOUDFLARE_API_TOKEN");

  if (!apiToken) {
    return new Response(
      JSON.stringify({ error: "Missing CLOUDFLARE_API_TOKEN in environment." }),
      { status: 500, headers: corsHeaders }
    );
  }

  try {
    const radarData = await fetchRadarData(apiToken);

    return new Response(JSON.stringify({ data: radarData }), {
      status: 200,
      headers: {
        ...corsHeaders,
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    console.error("Unhandled error:", error);
    return new Response(
      JSON.stringify({ error: "Failed to fetch Cloudflare Radar data." }),
      { status: 500, headers: corsHeaders }
    );
  }
});




